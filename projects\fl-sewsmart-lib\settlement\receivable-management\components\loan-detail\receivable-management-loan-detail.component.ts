import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { finalize } from 'rxjs';
import { FlcTableHelperService, FlcUtilService, FlcValidatorService } from 'fl-common-lib';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { initReceivableDetailHeaders } from './receivable-management-loan-detail.config';
import { PageEditModeEnum, ReceivableDetail } from '../../models/receivable-detail.interface';
import { ReceivableService } from '../../receivable-service';
import { OutboundSelectorDrawerComponent } from '../outbound-selector-drawer/outbound-selector-drawer.component'
import { ReceivableSubjectEventType } from '../../models/receivable-detail.enum';
import { ReceivableManagementBaseinfoComponent } from '../baseinfo/receivable-management-baseinfo.component';

const version = '1.0.2';

@Component({
  selector: 'flss-receivable-management-loan-detail',
  templateUrl: './receivable-management-loan-detail.component.html',
  styleUrls: ['./receivable-management-loan-detail.component.scss'],
})
export class ReceivableManagementLoanDetailComponent implements OnInit {
  @Input() editMode: PageEditModeEnum = PageEditModeEnum.add;
  @Input() detailInfo?: ReceivableDetail;
  @Input() baseinfoForm?: ReceivableManagementBaseinfoComponent;

  disabledBtn = false;

  // 当前汇率和税率（用于计算）
  private currentExchangeRate: number = 1;
  private currentTaxRate: number = 0.13;

  /**
   * 应收明细表格总计
   */
  statisticData = {
    qty: 0, // 出库数量
    exclude_tax_money: 0, // 不含税金额
    exclude_tax_local: 0, // 不含税本币金额
    tax_amount: 0, // 税额
    tax_amount_local: 0, // 本币税额
    total_money: 0, // 含税金额
    total_money_local: 0, // 含税本币金额
    receivable_money: 0, // 应收金额
    receivable_money_local: 0, // 应收本币金额
    adjustment_amount_included: 0, // 调整金额(计入)
    adjustment_amount_excluded: 0, // 调整金额(不计入)
  };

  constructor(
    private _fb: FormBuilder,
    private _drawerService: NzDrawerService,
    private _noticeService: NzNotificationService,
    private _messageService: NzMessageService,
    private _tableHelper: FlcTableHelperService,
    private _flcValidatorService: FlcValidatorService,
    private _flcUtil: FlcUtilService,
    private _service: ReceivableService
  ) {}

  pageEditModeEnum = PageEditModeEnum;
  subscribeKey = 'receivable-loan-detail';


  receivableForm: FormGroup = this._fb.group({
    outbound_order_list: this._fb.array([]),
  });

  get outbound_order_list(): FormArray {
    return this.receivableForm?.get('outbound_order_list') as FormArray;
  }

  ngOnInit() {
    this.headers = this._tableHelper.getFlcTableHeaderConfig<any>(initReceivableDetailHeaders(), version);
    this.getRenderHeaders();

    // 初始化汇率和税率
    this.initializeRatesFromDetail();
    this._service.addSubjectListener(
          this.subscribeKey,
          [
            ReceivableSubjectEventType.hasFactoryInfo,
            ReceivableSubjectEventType.adjustmentPriceChange,
            ReceivableSubjectEventType.exchangeRate,
            ReceivableSubjectEventType.invoiceTaxRate
          ],
          (res) => {
            if (res.type === ReceivableSubjectEventType.hasFactoryInfo) {
              // this.disabledBtn = !res.data.factory_code;
              // this.factory = res.data;
              // this.product_inbound_list.clear();
            }
            if (res.type === ReceivableSubjectEventType.adjustmentPriceChange) {
              if (!res.data) {
                // 全局调整项变化，重新计算所有行
                this.outbound_order_list.controls.forEach((_control: any) => {
                  const _lines = _control.get('lines') as FormArray;
                  _lines?.controls.forEach((lineControl: any) => {
                    this.calculateAdjustmentAmounts(lineControl as FormGroup);
                    this.calculateLineTotal(lineControl as FormGroup);
                  });
                });
                this.calculateStatistic();
              } else {
                // 单行调整项变化，只计算该行
                this.calculateAdjustmentAmounts(res.data);
                this.calculateLineTotal(res.data);
                this.calculateStatistic();
              }
            }
            if (res.type === ReceivableSubjectEventType.exchangeRate) {
              // 汇率变化时重新计算所有行的金额
              this.onExchangeRateChange(res.data.exchange_rate);
            }
            if (res.type === ReceivableSubjectEventType.invoiceTaxRate) {
              // 税率变化时重新计算所有行的金额
              this.onInvoiceTaxRateChange(res.data.invoice_tax_rate);
            }
          }
        );

    // 初始化统计数据
    this.calculateStatistic();

    // 重置删除的ID列表
    this.resetDeletedIds();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.detailInfo?.currentValue) {
      // 更新汇率和税率
      this.initializeRatesFromDetail();

      // 初始化明细数据
      this.initializeDetailLines();

      // 如果有详情数据，重新计算所有行的调整金额
      if (this.detailInfo && this.outbound_order_list?.controls.length > 0) {
        this.outbound_order_list.controls.forEach((outboundControl: any) => {
          const linesArray = outboundControl.get('lines') as FormArray;
          linesArray?.controls.forEach((lineControl: any) => {
            this.calculateAdjustmentAmounts(lineControl as FormGroup);
            this.calculateLineTotal(lineControl as FormGroup);
          });
        });
        this.calculateStatistic();
      }
    }
  }
/**
   * 重新获取渲染货款明细表头
   * @param event
   */
  headers: any[] = [];
  renderHeaders: any[] = [];
  btnHighLight = false;
  renderScrollX = '100%';
  tableHeight = 0;
  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    this._tableHelper
      .openTableHeaderMidifyDialog<any>(shadow, event.target as HTMLElement)
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = initReceivableDetailHeaders().find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          this._tableHelper.saveFlcTableHeaderConfig(this.headers, version);
        }
      });
  }

  onResize({ width }: NzResizeEvent, col: string): void {
    this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version);
    this.getRenderHeaders();
    // 更新输入框宽度
    this.updateInputWidths();
  }

  /**
   * 更新输入框宽度以适应列宽变化
   */
  private updateInputWidths(): void {
    // 使用 setTimeout 确保 DOM 更新完成
    setTimeout(() => {
      this.renderHeaders.forEach(header => {
        if (header.visible && this.isInputColumn(header.key)) {
          this.setColumnWidth(header.key, header.width);
        }
      });
    }, 100); // 增加延迟确保DOM完全渲染
  }

  /**
   * 判断是否为输入框列
   */
  private isInputColumn(key: string): boolean {
    const inputColumns = ['qty', 'price', 'exclude_tax_price', 'real_price', 'remark'];
    return inputColumns.includes(key);
  }

  /**
   * 设置列宽度（通过CSS变量的方式更优雅）
   */
  private setColumnWidth(columnKey: string, width: string): void {
    // 通过CSS自定义属性设置列宽
    const numericWidth = parseInt(width.replace('px', ''));
    const minWidth = 60; // 最小宽度
    const padding = 16; // 内边距
    const finalWidth = Math.max(numericWidth - padding, minWidth);

    // 设置CSS变量
    document.documentElement.style.setProperty(`--column-${columnKey}-width`, `${finalWidth}px`);

    // 直接通过CSS类控制，更加高效
    const cells = document.querySelectorAll(`[data-column="${columnKey}"]`);
    cells.forEach((cell: any) => {
      if (cell) {
        cell.style.setProperty('--input-width', `${finalWidth}px`);
      }
    });
  }
  private getRenderHeaders() {
    this.renderHeaders = this._tableHelper.getRenderHeader<any>(this.headers);
    this.renderScrollX = this._tableHelper.getRenderHeaderScrollWidth(this.renderHeaders) + 'px';
    // 在表头更新后，更新输入框宽度
    this.updateInputWidths();
  }


  private createLineGroup(line: any,code:string): FormGroup {
    const lineGroup = this._fb.group({
      id: [line.id || '0'],
      order_uuid: [line.order_uuid],
      order_code: [line.order_code],
      order_id: [line.order_id],
      outbound_code: [code], // 添加出库单号字段
      style_code: [line.style_code],
      po_code: [line.po_code],
      po_id: [line.po_id],
      po_unique_code: [line.po_unique_code],
      category: [line.category],
      currency_id:[line.currency_id],
      currency_name:[line.currency_name],
      color_name: [line.color_name],
      color_id: [line.color_id],
      spec_name: [line.spec_name],
      spec_id: [line.spec_id],
      sku_id: [line.sku_id],
      qty: [line.qty, [Validators.min(0)]],
      price: [line.unit_price, [Validators.min(0)]],
      total_money:[line.total_money   || 0],
      total_money_local:[line.total_money_local || 0],
      receivable_money: [line.receivable_money  || 0],
      receivable_money_local: [line.receivable_money_local || 0],
      adjustment_amount_included: [line.adjustment_amount_included || 0],
      adjustment_amount_excluded: [line.adjustment_amount_excluded || 0],
      exclude_tax_price: [line.exclude_tax_price || 0],
      exclude_tax_money: [line.exclude_tax_money || 0],
      exclude_tax_local: [line.exclude_tax_local || 0],
      tax_amount: [line.tax_amount || 0],
      tax_amount_local: [line.tax_amount_local || 0],
      adjustment_lines: this._fb.array([]), // 添加调整项FormArray
      adjustment_remark: [line.adjustment_remark || null], // 添加调整备注
      total_adjust: [line.total_adjust || 0], // 添加调整总计
    });

    // 计算调整金额
    this.calculateAdjustmentAmounts(lineGroup);
    this.calculateLineTotal(lineGroup);
    return lineGroup;
  }

  private calculateLineTotal(lineGroup: FormGroup, recalculateExcludeTaxPrice: boolean = true) {
    const qty = Number(lineGroup.get('qty')?.value) || 0;
    const price = Number(lineGroup.get('price')?.value) || 0;
    const adjustment_amount_included = Number(lineGroup.get('adjustment_amount_included')?.value) || 0;

    // 使用当前的汇率和税率，如果没有则使用详情信息中的值
    const taxRate = this.currentTaxRate || (Number(this.detailInfo?.invoice_tax_rate) / 100) || 0.13;
    const exchangeRate = this.currentExchangeRate || Number(this.detailInfo?.exchange_rate) || 1;

    const totalMoney = this._flcUtil.accMul(qty, price); // 金额
    const total_money_local = this._flcUtil.accDiv(totalMoney, exchangeRate); // 本币金额

    let excludeTaxPrice: number;
    let excludeTaxMoney: any;
    let exclude_tax_local: any;
    let taxAmount: any;
    let tax_amount_local: any;

    if (recalculateExcludeTaxPrice) {
      // 根据单价计算不含税单价
      excludeTaxPrice = price / (1 + taxRate);  // 不含税单价
    } else {
      // 使用现有的不含税单价
      excludeTaxPrice = Number(lineGroup.get('exclude_tax_price')?.value) || 0;
    }

    excludeTaxMoney = this._flcUtil.accMul(qty, excludeTaxPrice);   // 不含税金额
    exclude_tax_local = this._flcUtil.accDiv(excludeTaxMoney, exchangeRate); // 不含税本币金额
    taxAmount =  this._flcUtil.accSub(totalMoney, excludeTaxMoney); // 税额
    tax_amount_local = this._flcUtil.accSub(total_money_local, exclude_tax_local); // 本币税额

    // 修正计算逻辑：应收金额 = 金额 + (调整金额（计入） * 汇率)
    const adjustmentInForeignCurrency = this._flcUtil.accMul(adjustment_amount_included, exchangeRate); // 调整金额（计入）* 汇率
    const receivable_money = this._flcUtil.accAdd(totalMoney, adjustmentInForeignCurrency); // 应收金额
    const receivable_money_local = this._flcUtil.accAdd(Number(total_money_local), adjustment_amount_included); // 应收本币金额

    // 构建更新对象
    const updateValues: any = {
      exclude_tax_money: Number(excludeTaxMoney).toFixed(2),
      exclude_tax_local: Number(exclude_tax_local).toFixed(2),
      tax_amount: Number(taxAmount).toFixed(2),
      tax_amount_local: Number(tax_amount_local).toFixed(2),
      total_money: Number(totalMoney).toFixed(2),
      total_money_local: Number(total_money_local).toFixed(3),
      receivable_money: Number(receivable_money).toFixed(2),
      receivable_money_local: Number(receivable_money_local).toFixed(2),
    };

    // 只有在需要重新计算不含税单价时才更新该字段
    if (recalculateExcludeTaxPrice) {
      updateValues.exclude_tax_price = excludeTaxPrice.toFixed(3);
    }

    for (const key in updateValues) {
      lineGroup?.get(key)?.setValue(updateValues?.[key], { emitViewToModelChange: false })
    }
    // lineGroup.patchValue(updateValues, { emitEvent: false });
  }

  /**
   * 根据不含税单价计算其他金额（当用户修改不含税单价时调用）
   * @param lineGroup 行表单组
   */
  private calculateLineTotalFromExcludeTaxPrice(lineGroup: FormGroup) {
    const qty = Number(lineGroup.get('qty')?.value) || 0;
    const excludeTaxPrice = Number(lineGroup.get('exclude_tax_price')?.value) || 0;
    const adjustment_amount_included = Number(lineGroup.get('adjustment_amount_included')?.value) || 0;

    // 使用当前的汇率和税率
    const taxRate = this.currentTaxRate || (Number(this.detailInfo?.invoice_tax_rate) / 100) || 0.13;
    const exchangeRate = this.currentExchangeRate || Number(this.detailInfo?.exchange_rate) || 1;

    console.log(exchangeRate, taxRate,exchangeRate);
    // 根据不含税单价计算单价：单价 = 不含税单价 * (1 + 税率)
    const price = excludeTaxPrice * (1 + taxRate);
    const totalMoney = this._flcUtil.accMul(qty, price); // 金额
    const total_money_local = this._flcUtil.accDiv(totalMoney, exchangeRate); // 本币金额
    const excludeTaxMoney = this._flcUtil.accMul(qty, excludeTaxPrice);   // 不含税金额
    const exclude_tax_local = this._flcUtil.accDiv(excludeTaxMoney, exchangeRate); // 不含税本币金额
    const taxAmount =  this._flcUtil.accSub(totalMoney, excludeTaxMoney); // 税额
    const tax_amount_local = this._flcUtil.accSub(total_money_local, exclude_tax_local); // 本币税额

    // 修正计算逻辑：应收金额 = 金额 + (调整金额（计入） * 汇率)
    const adjustmentInForeignCurrency = this._flcUtil.accMul(adjustment_amount_included, exchangeRate); // 调整金额（计入）* 汇率
    const receivable_money = this._flcUtil.accAdd(totalMoney, adjustmentInForeignCurrency); // 应收金额
    const receivable_money_local = this._flcUtil.accAdd(Number(total_money_local), adjustment_amount_included); // 应收本币金额

    // 更新表单值，注意这里更新了price字段
    lineGroup?.get('price')?.setValue(price.toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('exclude_tax_money')?.setValue(Number(excludeTaxMoney).toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('exclude_tax_local')?.setValue(Number(exclude_tax_local).toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('tax_amount')?.setValue(Number(taxAmount).toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('tax_amount_local')?.setValue(Number(tax_amount_local).toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('total_money')?.setValue(Number(totalMoney).toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('total_money_local')?.setValue(Number(total_money_local).toFixed(3), { emitViewToModelChange: false })
    lineGroup?.get('receivable_money')?.setValue(Number(receivable_money).toFixed(2), { emitViewToModelChange: false })
    lineGroup?.get('receivable_money_local')?.setValue(Number(receivable_money_local).toFixed(2), { emitViewToModelChange: false })
    // lineGroup.patchValue({
    //   price: price.toFixed(2), // 根据不含税单价计算出的单价，保留2位小数
    //   exclude_tax_money: Number(excludeTaxMoney).toFixed(2),
    //   exclude_tax_local: Number(exclude_tax_local).toFixed(2),
    //   tax_amount: Number(taxAmount).toFixed(2),
    //   tax_amount_local: Number(tax_amount_local).toFixed(2),
    //   total_money: Number(totalMoney).toFixed(2),
    //   total_money_local: Number(total_money_local).toFixed(3),
    //   receivable_money: Number(receivable_money).toFixed(2),
    //   receivable_money_local: Number(receivable_money_local).toFixed(2),
    // }, { emitEvent: false });
  }

  private calculateStatistic() {
    this.statisticData = {
      qty: 0,
      exclude_tax_money: 0,
      exclude_tax_local: 0,
      tax_amount: 0,
      tax_amount_local: 0,
      total_money: 0,
      total_money_local: 0,
      receivable_money: 0,
      receivable_money_local: 0,
      adjustment_amount_included: 0,
      adjustment_amount_excluded: 0,
    };

    this.outbound_order_list?.controls.forEach((outboundControl) => {
      const linesArray = outboundControl.get('lines') as FormArray;
      linesArray?.controls.forEach((lineControl) => {
        this.statisticData.qty += Number(lineControl.get('qty')?.value) || 0;
        this.statisticData.exclude_tax_money += Number(lineControl.get('exclude_tax_money')?.value) || 0;
        this.statisticData.exclude_tax_local += Number(lineControl.get('exclude_tax_local')?.value) || 0;
        this.statisticData.tax_amount += Number(lineControl.get('tax_amount')?.value) || 0;
        this.statisticData.tax_amount_local += Number(lineControl.get('tax_amount_local')?.value) || 0;
        this.statisticData.total_money += Number(lineControl.get('total_money')?.value) || 0;
        this.statisticData.total_money_local += Number(lineControl.get('total_money_local')?.value) || 0;
        this.statisticData.receivable_money += Number(lineControl.get('receivable_money')?.value) || 0;
        this.statisticData.receivable_money_local += Number(lineControl.get('receivable_money_local')?.value) || 0;
        this.statisticData.adjustment_amount_included += Number(lineControl.get('adjustment_amount_included')?.value) || 0;
        this.statisticData.adjustment_amount_excluded += Number(lineControl.get('adjustment_amount_excluded')?.value) || 0;
      });
    });
  }

  /**
   * 获取已选择的出库单ID列表
   */
  getSelectedOutboundIds(): string[] {
    const selectedIds: string[] = [];
    this.outbound_order_list?.controls.forEach((outboundControl) => {
      const outboundId = outboundControl.get('id')?.value;
      if (outboundId) {
        selectedIds.push(outboundId);
      }
    });
    // console.log('已选择的出库单ID列表:', selectedIds);
    return selectedIds;
  }

  // 添加出库单
  onAddOutbound() {
    // 获取当前基础信息中的客户和币种
    const currentCustomerId = this.baseinfoForm?.baseInfoForm?.get('customer_id')?.value;
    const currentCustomerName = this.baseinfoForm?.baseInfoForm?.get('customer_name')?.value;
    const currentCurrencyId = this.baseinfoForm?.baseInfoForm?.get('currency_id')?.value;
    const hasExistingData = this.outbound_order_list.controls.length > 0;
    const drawerRef = this._drawerService.create({
      nzTitle: '选择出库单',
      nzContent: OutboundSelectorDrawerComponent,
      nzContentParams: {
        currentCustomerId: currentCustomerId,
        currentCustomerName: currentCustomerName,
        currentCurrencyId: currentCurrencyId,
        hasExistingData: hasExistingData,
        historySelectedIds: this.getSelectedOutboundIds(),
      },
      nzHeight: window.innerHeight * 0.8,
      nzMaskClosable: false,
      nzWrapClassName: 'modal-outer-order-selector',
      nzPlacement: 'bottom',
    });

    drawerRef.afterClose.subscribe((result: any) => {
      if (result?.length) {
        const firstOutbound = result[0];

        // 发送客户信息变更事件给 baseinfo 组件
          // if (firstOutbound.customer_id && firstOutbound.customer_name) {
          //   this._service.sendSubjectEvent(ReceivableSubjectEventType.customerInfo, {
          //     customer_id: firstOutbound.customer_id,
          //     customer_name: firstOutbound.customer_name
          //   });
          // }


        // 切换出库单时，总是带出所选出库单对应的币种
        if (firstOutbound && firstOutbound.orders[0].currency_id && firstOutbound.orders[0].currency_name) {
          // 通过事件通知baseinfo组件设置币种信息
          this._service.sendSubjectEvent(ReceivableSubjectEventType.currencyInfo, {
            currency_id: firstOutbound.orders[0].currency_id,
            currency_name: firstOutbound.orders[0].currency_name
          });
        }

        // 创建明细行
        this.createLine(result);

        // 发送添加订单事件
        this._service.sendSubjectEvent(ReceivableSubjectEventType.addOrder, {
          form: this.receivableForm,
          outboundData: result
        });
      }
    });
  }

  createLine(lines: any[]) {
   lines.forEach(item => {
    let lineForm :FormGroup;
    lineForm = this.createLineOfDetail(item);
    lineForm && this.outbound_order_list.push(lineForm);
   });
   // 添加出库单后重新计算汇总
   this.calculateStatistic();
  }

  createLineOfDetail(item: any) {
    const lineFrom = this._fb.group({
      id: item.id,
      code:item.code,
      outbound_code: item.code,
      outbound_time: item.outbound_time,
      customer_name: item.customer_name,
      customer_id:item.customer_id,
      lines: this._fb.array([]),
    });
    item.orders.forEach((line: any) => {
      const lineGroup = this.createLineGroup(line,item.code); 
      (lineFrom.get('lines') as FormArray)?.push(lineGroup);
    });
    return lineFrom;
  }

  onChangeRealPrice(control: any) {
    this.calculateLineTotal(control as FormGroup);
    this.calculateStatistic();
  }

  /**
   * 处理不含税单价变化
   * @param control 表单控件
   */
  onChangeExcludeTaxPrice(control: any) {
    this.calculateLineTotalFromExcludeTaxPrice(control as FormGroup);
    this.calculateStatistic();
  }

  /**
   * 检查出库单下的指定字段是否可以合并显示
   * @param outboundControl 出库单表单控件
   * @param fieldKey 字段名
   * @returns 是否可以合并
   */
  canMergeField(outboundControl: any, fieldKey: string): boolean {
    const linesArray = outboundControl.get('lines') as FormArray;
    if (!linesArray || linesArray.length <= 1) {
      return true; // 只有一行或没有行时可以合并
    }

    const firstValue = linesArray.at(0)?.get(fieldKey)?.value;
    return linesArray.controls.every((lineControl: any) =>
      lineControl.get(fieldKey)?.value === firstValue
    );
  }

  // 删除出库单行
  deleteLine(index: number) {
    const outboundControl = this.outbound_order_list.at(index);
    if (outboundControl) {
      const linesArray = outboundControl.get('lines') as FormArray;
      // 收集要删除的明细行ID
      linesArray?.controls.forEach((lineControl: any) => {
        const lineId = lineControl.get('id')?.value;
        if (lineId && lineId !== '0') {
          this.deletedLineIds.push(lineId);
        }
      });
    }

    this.outbound_order_list.removeAt(index);
    this.calculateStatistic();
  }

  /**
   * 删除单个明细行
   * @param outboundIndex 出库单索引
   * @param lineIndex 明细行索引
   */
  deleteDetailLine(outboundIndex: number, lineIndex: number) {
    const outboundControl = this.outbound_order_list.at(outboundIndex);
    if (outboundControl) {
      const linesArray = outboundControl.get('lines') as FormArray;
      const lineControl = linesArray?.at(lineIndex);

      // 记录要删除的明细行ID
      const lineId = lineControl?.get('id')?.value;
      if (lineId && lineId !== '0') {
        this.deletedLineIds.push(lineId);
      }

      linesArray?.removeAt(lineIndex);

      // 如果出库单下没有明细行了，删除整个出库单
      if (linesArray?.length === 0) {
        this.outbound_order_list.removeAt(outboundIndex);
      }

      this.calculateStatistic();
    }
  }


  // 删除的明细行ID列表
  private deletedLineIds: string[] = [];

  // 获取表单数据
  getFormData() {
    const _formartValue: any = {
      lines: [],
      deleted_lines: this.deletedLineIds // 添加删除的明细行ID
    };

    const _values = this.outbound_order_list.getRawValue();
    _values.forEach((outboundGroup: any) => {
      // 遍历每个出库单的明细行
      outboundGroup.lines.forEach((line: any) => {
        const receivableLine: any = {
          // 基础字段
          id: line.id || '0',
          order_uuid: line.order_uuid,
          po_unique_code: line.po_unique_code,
          outbound_id: outboundGroup.id,
          outbound_code: outboundGroup.code,
          sku_id: line.sku_id,

          // 数量和价格
          qty: line.qty?.toString() || '0',
          price: line.price?.toString() || '0',

          // 金额相关
          total_money: line.total_money?.toString() || '0',
          total_money_local: line.total_money_local?.toString() || '0',
          receivable_money: line.receivable_money?.toString() || '0',
          receivable_money_local: line.receivable_money_local?.toString() || '0',

          // 不含税相关
          exclude_tax_price: line.exclude_tax_price?.toString() || '0',
          exclude_tax_money: line.exclude_tax_money?.toString() || '0',
          exclude_tax_local: line.exclude_tax_local?.toString() || '0',

          // 税额相关
          tax_amount: line.tax_amount?.toString() || '0',
          tax_amount_local: line.tax_amount_local?.toString() || '0',

          // 调整金额
          adjustment_amount_included: line.adjustment_amount_included?.toString() || '0',
          adjustment_amount_excluded: line.adjustment_amount_excluded?.toString() || '0',

          // 调整明细
          adjustment: this.formatAdjustmentLines(line.adjustment_lines, line.id || '0'),

          // 其他字段
          bills_receivable_id: this.detailInfo?.id?.toString() || '0',
          deleted_adjustment: this.getDeletedAdjustmentIds(line.adjustment_lines), // 删除的调整明细ID列表
        };



        _formartValue.lines.push(receivableLine);
      });
    });

    return _formartValue;
  }

  /**
   * 格式化调整明细数据
   * @param adjustmentLines 调整明细FormArray
   * @param receivableLineId 应收明细行ID
   * @returns 格式化后的调整明细数组
   */
  private formatAdjustmentLines(adjustmentLines: any, receivableLineId: string): any[] {
    if (!adjustmentLines || !Array.isArray(adjustmentLines)) {
      return [];
    }

    return adjustmentLines
      .filter((adjustment: any) => {
        // 只提交有价格的调整项
        return adjustment.adjustment_price && Number(adjustment.adjustment_price) !== 0;
      })
      .map((adjustment: any) => ({
        // 调整项ID：新建时为0，编辑时根据是否为新增加来判断
        id: adjustment.id?.toString() || '0',

        // 应收明细ID：新建时为0，编辑时根据是否为新增加来判断
        bills_receivable_outbound_rel_id: receivableLineId,

        // 调整项基础信息（对应选择调整项的数据）
        adjustment_item_type: adjustment.adjustment_item_type?.toString() || adjustment.adjustment_type?.toString() || '1', // 对应adjust_type
        adjustment_item_code: adjustment.adjustment_item_code || '', // 对应code
        adjustment_item_name: adjustment.adjustment_item_name || '', // 对应name
        adjustment_item_id: adjustment.adjustment_item_id?.toString() || '0',

        // 调整价格（输入的price字段）
        adjustment_price: adjustment.adjustment_price?.toString() || '0',
        adjustment_remark: adjustment.adjustment_remark || '',

        // 是否计入金额（判断当前调整项是否计入金额）
        count_adjustment: adjustment.count_adjustment || false,
      }));
  }

  /**
   * 获取删除的调整明细ID列表
   * @param adjustmentLines 调整明细FormArray
   * @returns 删除的调整明细ID数组
   */
  private getDeletedAdjustmentIds(adjustmentLines: any): string[] {
    // 这里应该从某个地方获取删除的调整明细ID列表
    // 暂时返回空数组，具体实现需要根据业务逻辑来处理
    return [];
  }

  /**
   * 重置删除的ID列表
   */
  private resetDeletedIds() {
    this.deletedLineIds = [];
  }


  // 验证表单
  validateForm(): boolean {
    if (this.outbound_order_list.length === 0) {
      this._messageService.warning('请添加应收明细');
      return false;
    }

    let isValid = true;
    this.outbound_order_list.controls.forEach((outboundControl, outboundIndex) => {
      const linesArray = outboundControl.get('lines') as FormArray;
      linesArray?.controls.forEach((lineControl, lineIndex) => {
        if (!lineControl.valid) {
          this._messageService.error(`第${outboundIndex + 1}个出库单第${lineIndex + 1}行数据有误`);
          isValid = false;
        }
      });
    });

    return isValid;
  }

  // 获取表格数据
  getTableData(): any[] {
    const data: any[] = [];
    this.outbound_order_list?.controls.forEach((outboundControl, outboundIndex) => {
      const linesArray = outboundControl.get('lines') as FormArray;
      linesArray?.controls.forEach((lineControl, lineIndex) => {
        data.push({
          outboundIndex,
          lineIndex,
          outboundControl,
          lineControl,
        });
      });
    });
    return data;
  }

  // 批量计算
  onBatchCalculate() {
    this.outbound_order_list?.controls.forEach((outboundControl, index) => {
      this.calculateOutboundTotal(outboundControl);
    });
    this.calculateStatistic();
    this._messageService.success('批量计算完成');
  }

  // 计算单个出库单
  onCalculateOutbound(outboundIndex: number) {
    const outboundControl = this.outbound_order_list.at(outboundIndex);
    this.calculateOutboundTotal(outboundControl);
    this.calculateStatistic();
    this._messageService.success('计算完成');
  }

  // 计算出库单总计
  private calculateOutboundTotal(outboundControl: any) {
    const linesArray = outboundControl.get('lines') as FormArray;
    linesArray?.controls.forEach((lineControl) => {
      // this.calculateLineTotal(lineControl);
    });
  }

  // 获取字段状态
  getFieldStatus(lineControl: FormGroup, fieldName: string): string {
    const control = lineControl.get(fieldName);
    if (control?.invalid && control?.touched) {
      return 'error';
    }
    if (control?.pending) {
      return 'validating';
    }
    return '';
  }

  // 检查行是否有错误
  hasLineError(lineControl: FormGroup): boolean {
    return lineControl.invalid && lineControl.touched;
  }

  // 检查行是否有警告
  hasLineWarning(lineControl: FormGroup): boolean {
    const qty = Number(lineControl.get('qty')?.value) || 0;
    const price = Number(lineControl.get('price')?.value) || 0;
    return qty === 0 || price === 0;
  }

  // 获取出库单状态颜色
  getOutboundStatusColor(outboundControl: any): string {
    const status = outboundControl.get('status')?.value;
    switch (status) {
      case 'completed': return 'green';
      case 'pending': return 'orange';
      case 'cancelled': return 'red';
      default: return 'blue';
    }
  }

  // 获取出库单状态文本
  getOutboundStatusText(outboundControl: any): string {
    const status = outboundControl.get('status')?.value;
    switch (status) {
      case 'completed': return '已完成';
      case 'pending': return '进行中';
      case 'cancelled': return '已取消';
      default: return '正常';
    }
  }


  test() {
  }

  /**
   * 处理汇率变化
   * @param exchangeRate 新的汇率
   */
  onExchangeRateChange(exchangeRate: number) {
    this.currentExchangeRate = exchangeRate;
    this.recalculateAllLines();
  }

  /**
   * 处理税率变化
   * @param taxRate 新的税率（百分比形式，如13表示13%）
   */
  onInvoiceTaxRateChange(taxRate: number) {
    // 将百分比转换为小数形式
    this.currentTaxRate = taxRate / 100;
    this.recalculateAllLines();
  }

  /**
   * 重新计算所有行的金额
   */
  private recalculateAllLines() {
    this.outbound_order_list?.controls.forEach((outboundControl) => {
      const linesArray = outboundControl.get('lines') as FormArray;
      linesArray?.controls.forEach((lineControl) => {
        this.calculateLineTotal(lineControl as FormGroup);
      });
    });
    this.calculateStatistic();
  }

  /**
   * 从详情信息初始化汇率和税率
   */
  private initializeRatesFromDetail() {
    if (this.detailInfo) {
      this.currentExchangeRate = Number(this.detailInfo.exchange_rate) || 1;
      this.currentTaxRate = (Number(this.detailInfo.invoice_tax_rate) / 100) || 0.13;
    }
  }

  /**
   * 初始化明细数据
   */
  private initializeDetailLines() {
    if (!this.detailInfo?.lines || this.detailInfo.lines.length === 0) {
      return;
    }

    // 清空现有数据
    this.outbound_order_list.clear();
    this.resetDeletedIds();

    // 按出库单分组明细数据
    const groupedByOutbound = this.groupLinesByOutbound(this.detailInfo.lines);

    // 为每个出库单创建表单组
    Object.keys(groupedByOutbound).forEach(outboundId => {
      const outboundLines = groupedByOutbound[outboundId];
      const firstLine = outboundLines[0];



      // 创建出库单表单组
      const outboundGroup = this._fb.group({
        id: [firstLine.outbound_id || ''],
        code: [firstLine.outbound_code || ''],
        outbound_code: [firstLine.outbound_code || ''],
        outbound_time: [firstLine.outbound_time || ''],
        customer_name: [firstLine.customer_name || ''],
        customer_id: [firstLine.customer_id || ''],
        lines: this._fb.array([])
      });

      // 为每个明细行创建表单组
      const linesArray = outboundGroup.get('lines') as FormArray;
      outboundLines.forEach((line) => {
        const lineGroup = this.createLineGroupFromDetail(line);
        linesArray.push(lineGroup);
      });

      this.outbound_order_list.push(outboundGroup);
    });

    // 重新计算统计数据
    this.calculateStatistic();



    // 发送添加订单事件，通知调整明细组件初始化数据
    this._service.sendSubjectEvent(ReceivableSubjectEventType.addOrder, {
      form: this.receivableForm,
      customer_code: this.detailInfo?.customer_id,
      customer_name: this.detailInfo?.customer_name,
      type: 'detail' // 标识这是详情数据
    });
  }

  /**
   * 按出库单分组明细数据
   */
  private groupLinesByOutbound(lines: any[]): { [key: string]: any[] } {
    const grouped: { [key: string]: any[] } = {};

    lines.forEach(line => {
      const outboundId = line.outbound_id || 'unknown';
      if (!grouped[outboundId]) {
        grouped[outboundId] = [];
      }
      grouped[outboundId].push(line);
    });

    return grouped;
  }

  /**
   * 从详情数据创建明细行表单组
   */
  private createLineGroupFromDetail(line: any): FormGroup {
    const lineGroup = this._fb.group({
      // 基础ID字段
      id: [line.id || '0'],
      order_uuid: [line.order_uuid || ''],
      order_id: [line.order_id || ''],
      po_unique_code: [line.po_unique_code || ''],
      po_id: [line.po_id || ''],
      sku_id: [line.sku_id || ''],

      // 显示字段 - 出库单号、订单号、交付单号等
      outbound_code: [line.outbound_code || ''],
      order_code: [line.order_code || ''],
      po_code: [line.po_code || ''],

      // 产品信息字段 - 款号、品名、颜色、尺码
      style_code: [line.style_code || ''],
      category: [line.category || ''],
      color_id: [line.color_id || ''],
      color_code: [line.color_code || ''],
      color_name: [line.color_name || ''],
      spec_id: [line.spec_id || ''],
      spec_code: [line.spec_code || ''],
      spec_name: [line.spec_name || ''],

      // 币种信息
      currency_id: [line.currency_id || ''],
      currency_name: [line.currency_name || ''],

      // 数量和价格字段
      qty: [Number(line.qty) || 0, [Validators.min(0)]],
      price: [Number(line.price) || 0, [Validators.min(0)]],

      // 金额字段
      total_money: [Number(line.total_money) || 0],
      total_money_local: [Number(line.total_money_local) || 0],
      receivable_money: [Number(line.receivable_money) || 0],
      receivable_money_local: [Number(line.receivable_money_local) || 0],

      // 税务相关字段
      exclude_tax_price: [Number(line.exclude_tax_price) || 0],
      exclude_tax_money: [Number(line.exclude_tax_money) || 0],
      exclude_tax_local: [Number(line.exclude_tax_local) || 0],
      tax_amount: [Number(line.tax_amount) || 0],
      tax_amount_local: [Number(line.tax_amount_local) || 0],

      // 调整金额字段
      adjustment_amount_included: [Number(line.adjustment_amount_included) || 0],
      adjustment_amount_excluded: [Number(line.adjustment_amount_excluded) || 0],

      // 调整项和备注
      adjustment_lines: this._fb.array([]), // 调整项数组
      adjustment_remark: [line.adjustment_remark || ''],
      total_adjust: [Number(line.total_adjust) || 0],

      // 其他可能需要的字段
      real_price: [Number(line.real_price) || 0],
      remark: [line.remark || ''],
    });

    // 初始化调整项数据
    if (line.adjustment && Array.isArray(line.adjustment)) {
      const adjustmentArray = lineGroup.get('adjustment_lines') as FormArray;
      line.adjustment.forEach((adj: any) => {
        const adjGroup = this._fb.group({
          // 调整项ID：从详情数据中获取
          id: [adj.id || '0'],

          // 应收明细ID：从详情数据中获取bills_receivable_outbound_rel_id
          bills_receivable_outbound_rel_id: [adj.bills_receivable_outbound_rel_id || line.id || '0'],

          // 调整项基础信息
          adjustment_item_id: [adj.adjustment_item_id || ''],
          adjustment_item_code: [adj.adjustment_item_code || ''],
          adjustment_item_name: [adj.adjustment_item_name || ''],

          // 调整项类型：支持两种字段名
          adjustment_item_type: [adj.adjustment_item_type || adj.adjustment_type || '1'],

          // 调整价格
          adjustment_price: [Number(adj.adjustment_price) || 0],

          // 是否计入（业务字段，不提交到后端）
          count_adjustment: [adj.count_adjustment || false],
        });
        adjustmentArray.push(adjGroup);
      });
    }

    // 计算调整金额和行总计
    this.calculateAdjustmentAmounts(lineGroup);
    this.calculateLineTotal(lineGroup);

    return lineGroup;
  }

  /**
   * 计算调整金额（计入和不计入）
   * @param lineControl 行控件
   */
  private calculateAdjustmentAmounts(lineControl: FormGroup) {
    const adjustmentLines = lineControl.get('adjustment_lines') as FormArray;
    if (!adjustmentLines) {
      // 如果没有调整项，设置为0
      lineControl.patchValue({
        adjustment_amount_included: 0,
        adjustment_amount_excluded: 0
      });
      return;
    }

    let includedAmount: number = 0;
    let excludedAmount: number = 0;

    const adjustmentValues = adjustmentLines.getRawValue() || [];
    adjustmentValues.forEach((item: any) => {
      const price = Number(item.adjustment_price) || 0;
      const isIncluded = item.count_adjustment === true;

      if (price !== 0) {
        if (item.adjustment_item_type === 1) {
          // 扣减项（负数）
          if (isIncluded) {
            includedAmount = Number(this._flcUtil.accSub(includedAmount, price));
          } else {
            excludedAmount = Number(this._flcUtil.accSub(excludedAmount, price));
          }
        } else {
          // 增加项（正数）
          if (isIncluded) {
            includedAmount = Number(this._flcUtil.accAdd(includedAmount, price));
          } else {
            excludedAmount = Number(this._flcUtil.accAdd(excludedAmount, price));
          }
        }
      }
    });

    // 更新调整金额字段
    lineControl.patchValue({
      adjustment_amount_included: Number(this._flcUtil.toFixed(includedAmount, 2)),
      adjustment_amount_excluded: Number(this._flcUtil.toFixed(excludedAmount, 2))
    });
  }
}
